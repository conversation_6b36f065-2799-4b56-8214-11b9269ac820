{"name": "finance-app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"dev": "expo start", "start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo-google-fonts/inter": "^0.2.3", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-voice/voice": "^3.2.4", "@supabase/supabase-js": "^2.39.3", "base-64": "^1.0.0", "expo": "~50.0.4", "expo-auth-session": "~5.4.0", "expo-camera": "~14.0.3", "expo-constants": "~15.4.5", "expo-file-system": "^18.1.11", "expo-font": "~11.10.2", "expo-linking": "~6.2.2", "expo-router": "~3.4.6", "expo-sharing": "^13.1.5", "expo-splash-screen": "~0.26.4", "expo-status-bar": "~1.11.1", "expo-web-browser": "^14.1.6", "git": "^0.1.5", "lucide-react-native": "^0.525.0", "md5": "^2.3.0", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.73.2", "react-native-dotenv": "^3.4.9", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.2", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-svg": "14.1.0", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.19.6"}, "devDependencies": {"@babel/core": "^7.20.0", "@expo/metro-config": "^0.20.17", "@types/base-64": "^1.0.2", "@types/md5": "^2.3.5", "@types/react": "~18.2.45", "ajv-keywords": "^5.1.0", "metro-config": "^0.83.1", "metro-core": "^0.83.1", "typescript": "^5.1.3"}, "private": true}